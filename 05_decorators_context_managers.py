"""
装饰器和上下文管理器教程
包含：函数装饰器、类装饰器、参数化装饰器、上下文管理器、contextlib
"""

import time
import functools
import logging
from contextlib import contextmanager, closing
import threading
from typing import Any, Callable

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 1. 基础装饰器
def simple_decorator(func):
    """简单装饰器示例"""
    def wrapper(*args, **kwargs):
        print(f"调用函数 {func.__name__} 之前")
        result = func(*args, **kwargs)
        print(f"调用函数 {func.__name__} 之后")
        return result
    return wrapper


def timing_decorator(func):
    """计时装饰器"""
    @functools.wraps(func)  # 保留原函数的元数据
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"函数 {func.__name__} 执行时间: {end_time - start_time:.4f} 秒")
        return result
    return wrapper


def logging_decorator(func):
    """日志装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger.info(f"调用函数 {func.__name__}，参数: args={args}, kwargs={kwargs}")
        try:
            result = func(*args, **kwargs)
            logger.info(f"函数 {func.__name__} 执行成功，返回值: {result}")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
    return wrapper


# 2. 参数化装饰器
def retry(max_attempts=3, delay=1):
    """重试装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        logger.error(f"函数 {func.__name__} 重试 {max_attempts} 次后仍然失败")
                        raise
                    logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}，{delay}秒后重试")
                    time.sleep(delay)
        return wrapper
    return decorator


def validate_types(**expected_types):
    """类型验证装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取函数参数名
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # 验证类型
            for param_name, expected_type in expected_types.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    if not isinstance(value, expected_type):
                        raise TypeError(f"参数 {param_name} 期望类型 {expected_type.__name__}，实际类型 {type(value).__name__}")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


def cache_result(max_size=128):
    """缓存装饰器"""
    def decorator(func):
        cache = {}
        cache_order = []
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 创建缓存键
            key = str(args) + str(sorted(kwargs.items()))
            
            if key in cache:
                print(f"缓存命中: {func.__name__}")
                return cache[key]
            
            # 计算结果
            result = func(*args, **kwargs)
            
            # 添加到缓存
            if len(cache) >= max_size:
                # 移除最旧的缓存项
                oldest_key = cache_order.pop(0)
                del cache[oldest_key]
            
            cache[key] = result
            cache_order.append(key)
            print(f"缓存存储: {func.__name__}")
            return result
        
        return wrapper
    return decorator


# 3. 类装饰器
class CountCalls:
    """计数装饰器类"""
    
    def __init__(self, func):
        self.func = func
        self.count = 0
        functools.update_wrapper(self, func)
    
    def __call__(self, *args, **kwargs):
        self.count += 1
        print(f"函数 {self.func.__name__} 被调用了 {self.count} 次")
        return self.func(*args, **kwargs)


class RateLimiter:
    """限流装饰器类"""
    
    def __init__(self, max_calls=5, time_window=60):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
        self.lock = threading.Lock()
    
    def __call__(self, func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with self.lock:
                now = time.time()
                # 清理过期的调用记录
                self.calls = [call_time for call_time in self.calls if now - call_time < self.time_window]
                
                if len(self.calls) >= self.max_calls:
                    raise Exception(f"调用频率过高，{self.time_window}秒内最多调用{self.max_calls}次")
                
                self.calls.append(now)
                return func(*args, **kwargs)
        return wrapper


# 4. 基础上下文管理器
class FileManager:
    """文件管理上下文管理器"""
    
    def __init__(self, filename, mode='r'):
        self.filename = filename
        self.mode = mode
        self.file = None
    
    def __enter__(self):
        print(f"打开文件: {self.filename}")
        self.file = open(self.filename, self.mode)
        return self.file
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        print(f"关闭文件: {self.filename}")
        if self.file:
            self.file.close()
        
        if exc_type:
            print(f"文件操作中发生异常: {exc_type.__name__}: {exc_val}")
        
        return False  # 不抑制异常


class TimerContext:
    """计时上下文管理器"""
    
    def __init__(self, name="操作"):
        self.name = name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        print(f"开始 {self.name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = time.time()
        duration = end_time - self.start_time
        print(f"{self.name} 完成，耗时: {duration:.4f} 秒")
        return False


class DatabaseTransaction:
    """数据库事务上下文管理器"""
    
    def __init__(self, connection):
        self.connection = connection
        self.transaction_started = False
    
    def __enter__(self):
        print("开始数据库事务")
        self.connection.begin_transaction()
        self.transaction_started = True
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.transaction_started:
            if exc_type is None:
                print("提交事务")
                self.connection.commit()
            else:
                print(f"回滚事务，原因: {exc_type.__name__}: {exc_val}")
                self.connection.rollback()
        return False


# 5. 使用contextlib的上下文管理器
@contextmanager
def temporary_directory():
    """临时目录上下文管理器"""
    import tempfile
    import shutil
    
    temp_dir = tempfile.mkdtemp()
    print(f"创建临时目录: {temp_dir}")
    try:
        yield temp_dir
    finally:
        print(f"删除临时目录: {temp_dir}")
        shutil.rmtree(temp_dir, ignore_errors=True)


@contextmanager
def suppress_stdout():
    """抑制标准输出"""
    import sys
    import os
    
    with open(os.devnull, 'w') as devnull:
        old_stdout = sys.stdout
        sys.stdout = devnull
        try:
            yield
        finally:
            sys.stdout = old_stdout


@contextmanager
def change_directory(path):
    """临时改变工作目录"""
    import os
    
    old_cwd = os.getcwd()
    try:
        os.chdir(path)
        print(f"切换到目录: {path}")
        yield path
    finally:
        os.chdir(old_cwd)
        print(f"恢复到目录: {old_cwd}")


# 6. 高级装饰器模式
def singleton(cls):
    """单例装饰器"""
    instances = {}
    lock = threading.Lock()
    
    def get_instance(*args, **kwargs):
        if cls not in instances:
            with lock:
                if cls not in instances:
                    instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    
    return get_instance


def property_decorator(func):
    """属性装饰器示例"""
    return property(func)


# 测试函数
@simple_decorator
@timing_decorator
def slow_function():
    """慢函数示例"""
    time.sleep(1)
    return "完成"


@logging_decorator
@retry(max_attempts=3, delay=0.5)
def unreliable_function(success_rate=0.3):
    """不可靠函数示例"""
    import random
    if random.random() < success_rate:
        return "成功"
    else:
        raise Exception("随机失败")


@validate_types(name=str, age=int, score=float)
def process_student(name, age, score):
    """处理学生信息"""
    return f"学生 {name}，{age}岁，分数: {score}"


@cache_result(max_size=3)
def expensive_calculation(n):
    """昂贵的计算"""
    print(f"执行昂贵计算: {n}")
    time.sleep(0.5)
    return n ** 2


@CountCalls
def greet(name):
    """问候函数"""
    return f"你好, {name}!"


@RateLimiter(max_calls=3, time_window=5)
def api_call():
    """API调用"""
    return "API响应"


@singleton
class ConfigManager:
    """配置管理器"""
    def __init__(self):
        self.config = {"debug": True}
        print("ConfigManager 实例创建")


# 模拟数据库连接
class MockConnection:
    def begin_transaction(self):
        pass
    
    def commit(self):
        pass
    
    def rollback(self):
        pass


def main():
    """主函数"""
    print("=== 装饰器和上下文管理器教程 ===\n")
    
    # 1. 基础装饰器测试
    print("=== 基础装饰器测试 ===")
    result = slow_function()
    print(f"结果: {result}\n")
    
    # 2. 重试装饰器测试
    print("=== 重试装饰器测试 ===")
    try:
        result = unreliable_function(0.8)
        print(f"结果: {result}")
    except Exception as e:
        print(f"最终失败: {e}")
    print()
    
    # 3. 类型验证装饰器测试
    print("=== 类型验证装饰器测试 ===")
    try:
        result = process_student("张三", 20, 95.5)
        print(f"✓ {result}")
        
        result = process_student("李四", "二十", 90.0)  # 类型错误
    except TypeError as e:
        print(f"✗ 类型错误: {e}")
    print()
    
    # 4. 缓存装饰器测试
    print("=== 缓存装饰器测试 ===")
    print(expensive_calculation(5))
    print(expensive_calculation(3))
    print(expensive_calculation(5))  # 缓存命中
    print(expensive_calculation(7))
    print()
    
    # 5. 类装饰器测试
    print("=== 类装饰器测试 ===")
    print(greet("Alice"))
    print(greet("Bob"))
    print(greet("Charlie"))
    print()
    
    # 6. 限流装饰器测试
    print("=== 限流装饰器测试 ===")
    for i in range(5):
        try:
            result = api_call()
            print(f"✓ 调用 {i+1}: {result}")
        except Exception as e:
            print(f"✗ 调用 {i+1}: {e}")
    print()
    
    # 7. 单例装饰器测试
    print("=== 单例装饰器测试 ===")
    config1 = ConfigManager()
    config2 = ConfigManager()
    print(f"是否为同一实例: {config1 is config2}")
    print()
    
    # 8. 上下文管理器测试
    print("=== 上下文管理器测试 ===")
    
    # 计时上下文管理器
    with TimerContext("数据处理"):
        time.sleep(0.5)
        print("处理数据中...")
    
    print()
    
    # 数据库事务上下文管理器
    connection = MockConnection()
    
    # 正常事务
    with DatabaseTransaction(connection):
        print("执行数据库操作...")
    
    # 异常事务
    try:
        with DatabaseTransaction(connection):
            print("执行数据库操作...")
            raise Exception("模拟数据库错误")
    except Exception:
        pass
    
    print()
    
    # 9. contextlib上下文管理器测试
    print("=== contextlib上下文管理器测试 ===")
    
    with temporary_directory() as temp_dir:
        print(f"在临时目录中工作: {temp_dir}")
    
    print("抑制输出测试:")
    print("这行会显示")
    with suppress_stdout():
        print("这行不会显示")
    print("这行会显示")
    
    print()


if __name__ == "__main__":
    main()
