"""
多线程和并发编程教程
包含：threading、queue、concurrent.futures、线程安全、同步机制
"""

import threading
import time
import queue
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests

# 1. 基础线程创建和使用
def simple_task(name, duration):
    """简单的任务函数"""
    print(f"任务 {name} 开始执行")
    time.sleep(duration)
    print(f"任务 {name} 执行完成，耗时 {duration} 秒")
    return f"任务 {name} 的结果"


def demonstrate_basic_threading():
    """演示基础线程使用"""
    print("=== 基础线程演示 ===")
    
    # 方法1: 使用Thread类
    thread1 = threading.Thread(target=simple_task, args=("线程1", 2))
    thread2 = threading.Thread(target=simple_task, args=("线程2", 3))
    
    # 启动线程
    start_time = time.time()
    thread1.start()
    thread2.start()
    
    # 等待线程完成
    thread1.join()
    thread2.join()
    
    end_time = time.time()
    print(f"总耗时: {end_time - start_time:.2f} 秒")
    print()


# 2. 继承Thread类
class CustomThread(threading.Thread):
    """自定义线程类"""
    
    def __init__(self, name, task_count):
        super().__init__()
        self.name = name
        self.task_count = task_count
        self.result = []
    
    def run(self):
        """线程执行的主要逻辑"""
        print(f"自定义线程 {self.name} 开始执行")
        for i in range(self.task_count):
            time.sleep(0.5)
            result = f"{self.name}-任务{i+1}"
            self.result.append(result)
            print(f"  {result} 完成")
        print(f"自定义线程 {self.name} 执行完成")


# 3. 线程安全问题演示
class UnsafeCounter:
    """不安全的计数器"""
    def __init__(self):
        self.count = 0
    
    def increment(self):
        for _ in range(100000):
            self.count += 1


class SafeCounter:
    """线程安全的计数器"""
    def __init__(self):
        self.count = 0
        self.lock = threading.Lock()
    
    def increment(self):
        for _ in range(100000):
            with self.lock:  # 使用锁保证线程安全
                self.count += 1


# 4. 生产者-消费者模式
class Producer(threading.Thread):
    """生产者"""
    def __init__(self, queue, name):
        super().__init__()
        self.queue = queue
        self.name = name
    
    def run(self):
        for i in range(5):
            item = f"{self.name}-产品{i+1}"
            self.queue.put(item)
            print(f"生产者 {self.name} 生产了 {item}")
            time.sleep(random.uniform(0.5, 1.5))
        
        # 发送结束信号
        self.queue.put(None)
        print(f"生产者 {self.name} 完成生产")


class Consumer(threading.Thread):
    """消费者"""
    def __init__(self, queue, name):
        super().__init__()
        self.queue = queue
        self.name = name
    
    def run(self):
        while True:
            item = self.queue.get()
            if item is None:
                # 收到结束信号
                self.queue.task_done()
                break
            
            print(f"消费者 {self.name} 消费了 {item}")
            time.sleep(random.uniform(0.5, 2.0))
            self.queue.task_done()
        
        print(f"消费者 {self.name} 完成消费")


# 5. 使用concurrent.futures
def download_simulation(url):
    """模拟下载任务"""
    print(f"开始下载: {url}")
    # 模拟下载时间
    time.sleep(random.uniform(1, 3))
    size = random.randint(100, 1000)
    print(f"下载完成: {url}, 大小: {size}KB")
    return {"url": url, "size": size, "status": "success"}


def demonstrate_thread_pool():
    """演示线程池的使用"""
    print("=== 线程池演示 ===")
    
    urls = [
        "http://example.com/file1.zip",
        "http://example.com/file2.zip", 
        "http://example.com/file3.zip",
        "http://example.com/file4.zip",
        "http://example.com/file5.zip"
    ]
    
    start_time = time.time()
    
    # 使用ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=3) as executor:
        # 提交任务
        future_to_url = {executor.submit(download_simulation, url): url for url in urls}
        
        # 获取结果
        results = []
        for future in as_completed(future_to_url):
            url = future_to_url[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f"{url} 下载失败: {exc}")
    
    end_time = time.time()
    print(f"所有下载完成，总耗时: {end_time - start_time:.2f} 秒")
    print(f"下载结果: {results}")
    print()


# 6. 线程同步机制
class DataProcessor:
    """数据处理器 - 演示各种同步机制"""
    
    def __init__(self):
        self.data = []
        self.lock = threading.Lock()
        self.condition = threading.Condition(self.lock)
        self.event = threading.Event()
        self.semaphore = threading.Semaphore(2)  # 最多2个线程同时访问
    
    def add_data(self, item):
        """添加数据（使用条件变量）"""
        with self.condition:
            self.data.append(item)
            print(f"添加数据: {item}")
            self.condition.notify_all()  # 通知等待的线程
    
    def wait_for_data(self, min_count):
        """等待数据达到最小数量"""
        with self.condition:
            while len(self.data) < min_count:
                print(f"等待数据，当前数量: {len(self.data)}")
                self.condition.wait()
            print(f"数据足够了，当前数量: {len(self.data)}")
    
    def process_with_semaphore(self, worker_id):
        """使用信号量限制并发数"""
        with self.semaphore:
            print(f"工作者 {worker_id} 开始处理")
            time.sleep(2)
            print(f"工作者 {worker_id} 处理完成")
    
    def signal_completion(self):
        """发送完成信号"""
        self.event.set()
        print("发送完成信号")
    
    def wait_for_completion(self):
        """等待完成信号"""
        print("等待完成信号...")
        self.event.wait()
        print("收到完成信号！")


def demonstrate_synchronization():
    """演示同步机制"""
    print("=== 同步机制演示 ===")
    
    processor = DataProcessor()
    
    # 条件变量示例
    def data_waiter():
        processor.wait_for_data(3)
    
    def data_adder():
        for i in range(5):
            time.sleep(0.5)
            processor.add_data(f"数据{i+1}")
    
    # 启动线程
    waiter_thread = threading.Thread(target=data_waiter)
    adder_thread = threading.Thread(target=data_adder)
    
    waiter_thread.start()
    time.sleep(0.1)  # 确保waiter先启动
    adder_thread.start()
    
    waiter_thread.join()
    adder_thread.join()
    
    print()
    
    # 信号量示例
    print("信号量示例（最多2个并发）:")
    semaphore_threads = []
    for i in range(4):
        t = threading.Thread(target=processor.process_with_semaphore, args=(i+1,))
        semaphore_threads.append(t)
        t.start()
    
    for t in semaphore_threads:
        t.join()
    
    print()


def main():
    """主函数"""
    print("=== 多线程和并发编程教程 ===\n")
    
    # 1. 基础线程
    demonstrate_basic_threading()
    
    # 2. 自定义线程类
    print("=== 自定义线程类演示 ===")
    custom_thread1 = CustomThread("工作线程A", 3)
    custom_thread2 = CustomThread("工作线程B", 4)
    
    custom_thread1.start()
    custom_thread2.start()
    
    custom_thread1.join()
    custom_thread2.join()
    
    print(f"线程A结果: {custom_thread1.result}")
    print(f"线程B结果: {custom_thread2.result}")
    print()
    
    # 3. 线程安全演示
    print("=== 线程安全演示 ===")
    
    # 不安全的计数器
    unsafe_counter = UnsafeCounter()
    threads = []
    for i in range(5):
        t = threading.Thread(target=unsafe_counter.increment)
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()
    
    print(f"不安全计数器结果: {unsafe_counter.count} (期望: 500000)")
    
    # 安全的计数器
    safe_counter = SafeCounter()
    threads = []
    for i in range(5):
        t = threading.Thread(target=safe_counter.increment)
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()
    
    print(f"安全计数器结果: {safe_counter.count} (期望: 500000)")
    print()
    
    # 4. 生产者-消费者
    print("=== 生产者-消费者演示 ===")
    q = queue.Queue(maxsize=3)  # 限制队列大小
    
    producer = Producer(q, "生产者1")
    consumer = Consumer(q, "消费者1")
    
    producer.start()
    consumer.start()
    
    producer.join()
    consumer.join()
    print()
    
    # 5. 线程池
    demonstrate_thread_pool()
    
    # 6. 同步机制
    demonstrate_synchronization()


if __name__ == "__main__":
    main()
