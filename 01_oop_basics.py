"""
面向对象编程基础教程
包含：类、对象、属性、方法、封装
"""

# 1. 基本类的定义
class Person:
    """人员类 - 演示基本的类定义"""
    
    # 类属性（所有实例共享）
    species = "Homo sapiens"
    population = 0
    
    def __init__(self, name, age, email):
        """构造方法 - 初始化对象"""
        # 实例属性（每个对象独有）
        self.name = name
        self.age = age
        self.email = email
        self._salary = 0  # 受保护属性（约定以_开头）
        self.__id = None  # 私有属性（以__开头）
        
        # 更新类属性
        Person.population += 1
        
    def introduce(self):
        """实例方法 - 自我介绍"""
        return f"你好，我是{self.name}，今年{self.age}岁"
    
    def celebrate_birthday(self):
        """实例方法 - 过生日"""
        self.age += 1
        print(f"{self.name}过生日了！现在{self.age}岁")
    
    # 封装：getter和setter方法
    def get_salary(self):
        """获取薪资"""
        return self._salary
    
    def set_salary(self, salary):
        """设置薪资（带验证）"""
        if salary < 0:
            raise ValueError("薪资不能为负数")
        self._salary = salary
    
    def set_id(self, id_number):
        """设置ID（私有属性的访问）"""
        if len(str(id_number)) != 6:
            raise ValueError("ID必须是6位数字")
        self.__id = id_number
    
    def get_id(self):
        """获取ID"""
        return self.__id
    
    @classmethod
    def get_population(cls):
        """类方法 - 获取总人口"""
        return cls.population
    
    @staticmethod
    def is_adult(age):
        """静态方法 - 判断是否成年"""
        return age >= 18
    
    def __str__(self):
        """字符串表示"""
        return f"Person(name='{self.name}', age={self.age})"
    
    def __repr__(self):
        """开发者友好的字符串表示"""
        return f"Person('{self.name}', {self.age}, '{self.email}')"


# 2. 使用Property装饰器实现更优雅的封装
class BankAccount:
    """银行账户类 - 演示Property的使用"""
    
    def __init__(self, account_number, initial_balance=0):
        self.account_number = account_number
        self._balance = initial_balance
        self._transaction_history = []
    
    @property
    def balance(self):
        """余额属性的getter"""
        return self._balance
    
    @balance.setter
    def balance(self, amount):
        """余额属性的setter（带验证）"""
        if amount < 0:
            raise ValueError("余额不能为负数")
        old_balance = self._balance
        self._balance = amount
        self._transaction_history.append(f"余额从{old_balance}变更为{amount}")
    
    @property
    def transaction_history(self):
        """只读属性 - 交易历史"""
        return self._transaction_history.copy()
    
    def deposit(self, amount):
        """存款"""
        if amount <= 0:
            raise ValueError("存款金额必须大于0")
        self._balance += amount
        self._transaction_history.append(f"存款: +{amount}")
        return self._balance
    
    def withdraw(self, amount):
        """取款"""
        if amount <= 0:
            raise ValueError("取款金额必须大于0")
        if amount > self._balance:
            raise ValueError("余额不足")
        self._balance -= amount
        self._transaction_history.append(f"取款: -{amount}")
        return self._balance


# 3. 演示封装的重要性
class BadExample:
    """不好的例子 - 没有封装"""
    def __init__(self):
        self.balance = 1000  # 直接暴露属性


class GoodExample:
    """好的例子 - 适当封装"""
    def __init__(self):
        self._balance = 1000  # 受保护属性
    
    @property
    def balance(self):
        return self._balance
    
    def deposit(self, amount):
        if amount > 0:
            self._balance += amount
        else:
            raise ValueError("存款金额必须大于0")


if __name__ == "__main__":
    print("=== 面向对象编程基础演示 ===\n")
    
    # 创建对象
    print("1. 创建Person对象")
    person1 = Person("张三", 25, "<EMAIL>")
    person2 = Person("李四", 30, "<EMAIL>")
    
    print(f"person1: {person1}")
    print(f"person2: {person2}")
    print(f"总人口: {Person.get_population()}")
    print()
    
    # 调用方法
    print("2. 调用实例方法")
    print(person1.introduce())
    person1.celebrate_birthday()
    print()
    
    # 访问属性
    print("3. 属性访问和封装")
    person1.set_salary(5000)
    print(f"{person1.name}的薪资: {person1.get_salary()}")
    
    person1.set_id(123456)
    print(f"{person1.name}的ID: {person1.get_id()}")
    print()
    
    # 静态方法和类方法
    print("4. 静态方法和类方法")
    print(f"25岁是否成年: {Person.is_adult(25)}")
    print(f"当前总人口: {Person.get_population()}")
    print()
    
    # Property示例
    print("5. Property装饰器示例")
    account = BankAccount("12345", 1000)
    print(f"初始余额: {account.balance}")
    
    account.deposit(500)
    print(f"存款后余额: {account.balance}")
    
    account.withdraw(200)
    print(f"取款后余额: {account.balance}")
    
    print("交易历史:")
    for transaction in account.transaction_history:
        print(f"  - {transaction}")
