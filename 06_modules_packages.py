"""
模块和包的组织教程
包含：模块导入、包结构设计、__init__.py文件、项目组织最佳实践
"""

import sys
import os
import importlib
from pathlib import Path

# 1. 模块导入的各种方式
def demonstrate_import_methods():
    """演示各种导入方法"""
    print("=== 模块导入方法演示 ===")
    
    # 1. 标准导入
    import math
    print(f"math.pi = {math.pi}")
    
    # 2. 从模块导入特定函数/类
    from math import sqrt, pow
    print(f"sqrt(16) = {sqrt(16)}")
    
    # 3. 导入并重命名
    import datetime as dt
    print(f"当前时间: {dt.datetime.now()}")
    
    # 4. 从模块导入所有（不推荐）
    # from math import *  # 会污染命名空间
    
    # 5. 相对导入（在包内使用）
    # from .submodule import function  # 同级目录
    # from ..parent_module import function  # 上级目录
    
    # 6. 动态导入
    module_name = "json"
    json_module = importlib.import_module(module_name)
    data = {"name": "Python", "version": "3.9"}
    json_str = json_module.dumps(data)
    print(f"动态导入JSON模块: {json_str}")
    
    print()


# 2. 模块搜索路径
def demonstrate_module_path():
    """演示模块搜索路径"""
    print("=== 模块搜索路径 ===")
    
    print("Python模块搜索路径:")
    for i, path in enumerate(sys.path):
        print(f"  {i+1}. {path}")
    
    # 添加自定义路径
    custom_path = "/custom/module/path"
    if custom_path not in sys.path:
        sys.path.append(custom_path)
        print(f"\n添加自定义路径: {custom_path}")
    
    print()


# 3. 创建示例包结构
def create_example_package_structure():
    """创建示例包结构"""
    print("=== 创建示例包结构 ===")
    
    # 定义包结构
    package_structure = {
        "myproject": {
            "__init__.py": '''"""
MyProject - 示例项目包
"""

__version__ = "1.0.0"
__author__ = "Your Name"

# 包级别的导入
from .core import Calculator
from .utils import helper_function

# 定义包的公共接口
__all__ = ["Calculator", "helper_function", "get_version"]

def get_version():
    return __version__
''',
            "core": {
                "__init__.py": '''"""
核心模块
"""

from .calculator import Calculator
from .processor import DataProcessor

__all__ = ["Calculator", "DataProcessor"]
''',
                "calculator.py": '''"""
计算器模块
"""

class Calculator:
    """简单计算器类"""
    
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        """加法"""
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def subtract(self, a, b):
        """减法"""
        result = a - b
        self.history.append(f"{a} - {b} = {result}")
        return result
    
    def multiply(self, a, b):
        """乘法"""
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result
    
    def divide(self, a, b):
        """除法"""
        if b == 0:
            raise ValueError("除数不能为零")
        result = a / b
        self.history.append(f"{a} / {b} = {result}")
        return result
    
    def get_history(self):
        """获取计算历史"""
        return self.history.copy()
    
    def clear_history(self):
        """清空历史"""
        self.history.clear()
''',
                "processor.py": '''"""
数据处理器模块
"""

from typing import List, Any
import json

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.data = []
    
    def load_data(self, data: List[Any]):
        """加载数据"""
        self.data = data.copy()
    
    def filter_data(self, condition):
        """过滤数据"""
        return [item for item in self.data if condition(item)]
    
    def transform_data(self, transformer):
        """转换数据"""
        return [transformer(item) for item in self.data]
    
    def save_to_json(self, filename: str):
        """保存为JSON文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.data, f, ensure_ascii=False, indent=2)
    
    def load_from_json(self, filename: str):
        """从JSON文件加载"""
        with open(filename, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
'''
            },
            "utils": {
                "__init__.py": '''"""
工具模块
"""

from .helpers import helper_function, format_number
from .validators import validate_email, validate_phone

__all__ = ["helper_function", "format_number", "validate_email", "validate_phone"]
''',
                "helpers.py": '''"""
辅助函数模块
"""

import re
from typing import Union

def helper_function(text: str) -> str:
    """辅助函数示例"""
    return f"处理文本: {text.upper()}"

def format_number(number: Union[int, float], decimal_places: int = 2) -> str:
    """格式化数字"""
    return f"{number:.{decimal_places}f}"

def slugify(text: str) -> str:
    """将文本转换为URL友好的格式"""
    text = text.lower()
    text = re.sub(r'[^a-z0-9]+', '-', text)
    return text.strip('-')

def chunk_list(lst: list, chunk_size: int) -> list:
    """将列表分块"""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]
''',
                "validators.py": '''"""
验证器模块
"""

import re
from typing import Optional

def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_phone(phone: str, country_code: str = "CN") -> bool:
    """验证手机号格式"""
    if country_code == "CN":
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, phone))
    return False

def validate_password(password: str, min_length: int = 8) -> tuple[bool, Optional[str]]:
    """验证密码强度"""
    if len(password) < min_length:
        return False, f"密码长度至少{min_length}位"
    
    if not re.search(r'[A-Z]', password):
        return False, "密码必须包含大写字母"
    
    if not re.search(r'[a-z]', password):
        return False, "密码必须包含小写字母"
    
    if not re.search(r'\d', password):
        return False, "密码必须包含数字"
    
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        return False, "密码必须包含特殊字符"
    
    return True, None
'''
            },
            "tests": {
                "__init__.py": "",
                "test_calculator.py": '''"""
计算器测试模块
"""

import unittest
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from myproject.core import Calculator

class TestCalculator(unittest.TestCase):
    """计算器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.calc = Calculator()
    
    def test_add(self):
        """测试加法"""
        result = self.calc.add(2, 3)
        self.assertEqual(result, 5)
    
    def test_subtract(self):
        """测试减法"""
        result = self.calc.subtract(5, 3)
        self.assertEqual(result, 2)
    
    def test_multiply(self):
        """测试乘法"""
        result = self.calc.multiply(4, 3)
        self.assertEqual(result, 12)
    
    def test_divide(self):
        """测试除法"""
        result = self.calc.divide(10, 2)
        self.assertEqual(result, 5.0)
    
    def test_divide_by_zero(self):
        """测试除零异常"""
        with self.assertRaises(ValueError):
            self.calc.divide(10, 0)
    
    def test_history(self):
        """测试历史记录"""
        self.calc.add(1, 2)
        self.calc.multiply(3, 4)
        history = self.calc.get_history()
        self.assertEqual(len(history), 2)
        self.assertIn("1 + 2 = 3", history)
        self.assertIn("3 * 4 = 12", history)

if __name__ == "__main__":
    unittest.main()
'''
            },
            "config": {
                "__init__.py": "",
                "settings.py": '''"""
配置设置模块
"""

import os
from pathlib import Path

# 项目根目录
BASE_DIR = Path(__file__).parent.parent

# 数据库配置
DATABASE = {
    "host": os.getenv("DB_HOST", "localhost"),
    "port": int(os.getenv("DB_PORT", "5432")),
    "name": os.getenv("DB_NAME", "myproject"),
    "user": os.getenv("DB_USER", "user"),
    "password": os.getenv("DB_PASSWORD", "password"),
}

# 日志配置
LOGGING = {
    "level": os.getenv("LOG_LEVEL", "INFO"),
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": BASE_DIR / "logs" / "app.log",
}

# API配置
API = {
    "host": os.getenv("API_HOST", "0.0.0.0"),
    "port": int(os.getenv("API_PORT", "8000")),
    "debug": os.getenv("DEBUG", "False").lower() == "true",
}
'''
            },
            "main.py": '''"""
主程序入口
"""

from myproject import Calculator, helper_function, get_version
from myproject.core import DataProcessor
from myproject.utils import validate_email, format_number

def main():
    """主函数"""
    print(f"MyProject 版本: {get_version()}")
    print()
    
    # 使用计算器
    calc = Calculator()
    print("=== 计算器演示 ===")
    print(f"2 + 3 = {calc.add(2, 3)}")
    print(f"10 - 4 = {calc.subtract(10, 4)}")
    print(f"5 * 6 = {calc.multiply(5, 6)}")
    print(f"15 / 3 = {calc.divide(15, 3)}")
    
    print("计算历史:")
    for record in calc.get_history():
        print(f"  {record}")
    print()
    
    # 使用工具函数
    print("=== 工具函数演示 ===")
    print(helper_function("hello world"))
    print(f"格式化数字: {format_number(3.14159, 3)}")
    print(f"邮箱验证: {validate_email('<EMAIL>')}")
    print()
    
    # 使用数据处理器
    print("=== 数据处理器演示 ===")
    processor = DataProcessor()
    data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    processor.load_data(data)
    
    # 过滤偶数
    even_numbers = processor.filter_data(lambda x: x % 2 == 0)
    print(f"偶数: {even_numbers}")
    
    # 转换数据（平方）
    squared_numbers = processor.transform_data(lambda x: x ** 2)
    print(f"平方: {squared_numbers}")

if __name__ == "__main__":
    main()
'''
        }
    }
    
    # 创建目录结构
    def create_structure(base_path, structure):
        for name, content in structure.items():
            path = base_path / name
            if isinstance(content, dict):
                path.mkdir(exist_ok=True)
                create_structure(path, content)
            else:
                path.parent.mkdir(parents=True, exist_ok=True)
                with open(path, 'w', encoding='utf-8') as f:
                    f.write(content)
    
    # 创建包结构
    project_root = Path("example_project")
    if not project_root.exists():
        create_structure(project_root, package_structure)
        print(f"✓ 创建示例项目结构: {project_root.absolute()}")
    else:
        print(f"✓ 示例项目已存在: {project_root.absolute()}")
    
    print()


# 4. 包的导入和使用示例
def demonstrate_package_usage():
    """演示包的使用"""
    print("=== 包的使用演示 ===")
    
    # 添加示例项目到路径
    project_path = Path("example_project").absolute()
    if str(project_path) not in sys.path:
        sys.path.insert(0, str(project_path))
    
    try:
        # 导入包
        import myproject
        print(f"包版本: {myproject.get_version()}")
        
        # 使用包中的类
        calc = myproject.Calculator()
        result = calc.add(10, 20)
        print(f"计算结果: {result}")
        
        # 使用工具函数
        formatted = myproject.helper_function("test")
        print(f"工具函数结果: {formatted}")
        
    except ImportError as e:
        print(f"导入失败: {e}")
        print("请确保示例项目结构已创建")
    
    print()


# 5. __init__.py 文件的作用
def explain_init_file():
    """解释__init__.py文件的作用"""
    print("=== __init__.py 文件的作用 ===")
    
    explanations = [
        "1. 标识目录为Python包",
        "2. 控制包的导入行为",
        "3. 定义包的公共接口 (__all__)",
        "4. 执行包级别的初始化代码",
        "5. 提供包的元数据 (__version__, __author__等)",
        "6. 简化导入路径（重新导出子模块）"
    ]
    
    for explanation in explanations:
        print(f"  {explanation}")
    
    print("\n__init__.py 示例内容:")
    print('''
# 包元数据
__version__ = "1.0.0"
__author__ = "Your Name"

# 导入子模块
from .core import Calculator
from .utils import helper_function

# 定义公共接口
__all__ = ["Calculator", "helper_function"]

# 包级别初始化
print("MyProject 包已加载")
''')
    print()


# 6. 项目组织最佳实践
def demonstrate_best_practices():
    """演示项目组织最佳实践"""
    print("=== 项目组织最佳实践 ===")
    
    best_practices = [
        "1. 使用有意义的包和模块名称",
        "2. 保持模块职责单一",
        "3. 使用__init__.py控制导入",
        "4. 将配置分离到独立模块",
        "5. 创建tests目录进行测试",
        "6. 使用相对导入在包内部",
        "7. 提供清晰的文档和示例",
        "8. 遵循PEP 8命名规范"
    ]
    
    for practice in best_practices:
        print(f"  {practice}")
    
    print("\n推荐的项目结构:")
    print('''
myproject/
├── myproject/              # 主包
│   ├── __init__.py        # 包初始化
│   ├── core/              # 核心功能
│   │   ├── __init__.py
│   │   ├── models.py
│   │   └── services.py
│   ├── utils/             # 工具模块
│   │   ├── __init__.py
│   │   ├── helpers.py
│   │   └── validators.py
│   └── config/            # 配置模块
│       ├── __init__.py
│       └── settings.py
├── tests/                 # 测试目录
│   ├── __init__.py
│   ├── test_core.py
│   └── test_utils.py
├── docs/                  # 文档目录
├── requirements.txt       # 依赖文件
├── setup.py              # 安装脚本
├── README.md             # 项目说明
└── main.py               # 入口文件
''')
    print()


def main():
    """主函数"""
    print("=== 模块和包的组织教程 ===\n")
    
    # 1. 导入方法演示
    demonstrate_import_methods()
    
    # 2. 模块搜索路径
    demonstrate_module_path()
    
    # 3. 创建示例包结构
    create_example_package_structure()
    
    # 4. 包的使用演示
    demonstrate_package_usage()
    
    # 5. __init__.py文件说明
    explain_init_file()
    
    # 6. 最佳实践
    demonstrate_best_practices()


if __name__ == "__main__":
    main()
