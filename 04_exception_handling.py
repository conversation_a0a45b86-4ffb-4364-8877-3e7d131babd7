"""
异常处理教程
包含：try-catch语法、自定义异常、异常传播、最佳实践
"""

import logging
import traceback
from typing import Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 1. 基础异常处理
def basic_exception_handling():
    """演示基础异常处理"""
    print("=== 基础异常处理 ===")
    
    # 基本try-except
    try:
        result = 10 / 0
    except ZeroDivisionError:
        print("捕获到除零错误")
    
    # 捕获多种异常
    try:
        numbers = [1, 2, 3]
        print(numbers[10])  # IndexError
    except (IndexError, KeyError) as e:
        print(f"捕获到索引或键错误: {e}")
    
    # 捕获所有异常
    try:
        int("abc")
    except Exception as e:
        print(f"捕获到异常: {type(e).__name__}: {e}")
    
    # try-except-else-finally
    try:
        result = 10 / 2
    except ZeroDivisionError:
        print("除零错误")
    else:
        print(f"计算成功，结果: {result}")
    finally:
        print("无论如何都会执行")
    
    print()


# 2. 自定义异常类
class CustomError(Exception):
    """自定义异常基类"""
    pass


class ValidationError(CustomError):
    """验证错误"""
    def __init__(self, message, field=None, value=None):
        super().__init__(message)
        self.field = field
        self.value = value
        self.message = message
    
    def __str__(self):
        if self.field:
            return f"验证错误 [{self.field}]: {self.message} (值: {self.value})"
        return f"验证错误: {self.message}"


class BusinessLogicError(CustomError):
    """业务逻辑错误"""
    def __init__(self, message, error_code=None):
        super().__init__(message)
        self.error_code = error_code
        self.message = message
    
    def __str__(self):
        if self.error_code:
            return f"业务错误 [{self.error_code}]: {self.message}"
        return f"业务错误: {self.message}"


class ResourceError(CustomError):
    """资源错误"""
    def __init__(self, message, resource_type=None, resource_id=None):
        super().__init__(message)
        self.resource_type = resource_type
        self.resource_id = resource_id
        self.message = message


# 3. 实际应用示例 - 用户管理系统
class User:
    """用户类"""
    def __init__(self, username, email, age):
        self.username = username
        self.email = email
        self.age = age


class UserManager:
    """用户管理器"""
    
    def __init__(self):
        self.users = {}
    
    def validate_user_data(self, username, email, age):
        """验证用户数据"""
        if not username or len(username) < 3:
            raise ValidationError("用户名长度至少3个字符", "username", username)
        
        if "@" not in email:
            raise ValidationError("邮箱格式不正确", "email", email)
        
        if not isinstance(age, int) or age < 0 or age > 150:
            raise ValidationError("年龄必须是0-150之间的整数", "age", age)
    
    def create_user(self, username, email, age):
        """创建用户"""
        try:
            # 验证数据
            self.validate_user_data(username, email, age)
            
            # 检查用户是否已存在
            if username in self.users:
                raise BusinessLogicError(f"用户 {username} 已存在", "USER_EXISTS")
            
            # 创建用户
            user = User(username, email, age)
            self.users[username] = user
            
            logger.info(f"用户 {username} 创建成功")
            return user
            
        except ValidationError as e:
            logger.error(f"用户创建失败 - 验证错误: {e}")
            raise  # 重新抛出异常
        except BusinessLogicError as e:
            logger.error(f"用户创建失败 - 业务错误: {e}")
            raise
        except Exception as e:
            logger.error(f"用户创建失败 - 未知错误: {e}")
            raise CustomError(f"创建用户时发生未知错误: {e}")
    
    def get_user(self, username):
        """获取用户"""
        if username not in self.users:
            raise ResourceError(f"用户 {username} 不存在", "User", username)
        return self.users[username]
    
    def update_user_age(self, username, new_age):
        """更新用户年龄"""
        try:
            user = self.get_user(username)
            
            if not isinstance(new_age, int) or new_age < 0 or new_age > 150:
                raise ValidationError("年龄必须是0-150之间的整数", "age", new_age)
            
            old_age = user.age
            user.age = new_age
            
            logger.info(f"用户 {username} 年龄从 {old_age} 更新为 {new_age}")
            return user
            
        except (ResourceError, ValidationError):
            raise  # 重新抛出已知异常
        except Exception as e:
            raise CustomError(f"更新用户年龄时发生错误: {e}")


# 4. 文件操作异常处理
class FileProcessor:
    """文件处理器"""
    
    @staticmethod
    def safe_read_file(filename):
        """安全读取文件"""
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                content = file.read()
                logger.info(f"成功读取文件: {filename}")
                return content
        except FileNotFoundError:
            logger.error(f"文件不存在: {filename}")
            raise ResourceError(f"文件 {filename} 不存在", "File", filename)
        except PermissionError:
            logger.error(f"没有权限读取文件: {filename}")
            raise ResourceError(f"没有权限读取文件 {filename}", "File", filename)
        except UnicodeDecodeError as e:
            logger.error(f"文件编码错误: {filename}, {e}")
            raise ValidationError(f"文件 {filename} 编码错误", "encoding", str(e))
        except Exception as e:
            logger.error(f"读取文件时发生未知错误: {filename}, {e}")
            raise CustomError(f"读取文件 {filename} 时发生未知错误: {e}")
    
    @staticmethod
    def safe_write_file(filename, content):
        """安全写入文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as file:
                file.write(content)
                logger.info(f"成功写入文件: {filename}")
        except PermissionError:
            raise ResourceError(f"没有权限写入文件 {filename}", "File", filename)
        except OSError as e:
            raise ResourceError(f"写入文件 {filename} 时发生系统错误: {e}", "File", filename)


# 5. 异常链和上下文管理
class DatabaseConnection:
    """模拟数据库连接"""
    
    def __init__(self, connection_string):
        self.connection_string = connection_string
        self.connected = False
    
    def connect(self):
        """连接数据库"""
        try:
            # 模拟连接过程
            if "invalid" in self.connection_string:
                raise ConnectionError("无效的连接字符串")
            self.connected = True
            logger.info("数据库连接成功")
        except ConnectionError as e:
            # 异常链：保留原始异常信息
            raise ResourceError("数据库连接失败", "Database", self.connection_string) from e
    
    def disconnect(self):
        """断开连接"""
        if self.connected:
            self.connected = False
            logger.info("数据库连接已断开")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()
        if exc_type:
            logger.error(f"在数据库操作中发生异常: {exc_type.__name__}: {exc_val}")
        return False  # 不抑制异常


# 6. 异常处理最佳实践
def demonstrate_best_practices():
    """演示异常处理最佳实践"""
    print("=== 异常处理最佳实践 ===")
    
    # 1. 具体异常优于通用异常
    def good_exception_handling(data):
        try:
            return int(data)
        except ValueError:
            logger.error(f"无法将 '{data}' 转换为整数")
            raise ValidationError(f"'{data}' 不是有效的整数", "data", data)
    
    # 2. 使用异常链保留原始错误信息
    def process_data_with_chain(data):
        try:
            result = good_exception_handling(data)
            return result * 2
        except ValidationError as e:
            raise BusinessLogicError("数据处理失败") from e
    
    # 3. 记录异常但不隐藏
    def log_and_reraise(func, *args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"调用 {func.__name__} 时发生异常: {e}")
            logger.error(f"异常详情:\n{traceback.format_exc()}")
            raise  # 重新抛出，不隐藏异常
    
    # 测试最佳实践
    try:
        result = log_and_reraise(process_data_with_chain, "abc")
    except BusinessLogicError as e:
        print(f"捕获到业务异常: {e}")
        print(f"原始异常: {e.__cause__}")
    
    print()


def main():
    """主函数"""
    print("=== 异常处理教程 ===\n")
    
    # 1. 基础异常处理
    basic_exception_handling()
    
    # 2. 用户管理系统示例
    print("=== 用户管理系统异常处理 ===")
    user_manager = UserManager()
    
    # 测试各种异常情况
    test_cases = [
        ("ab", "invalid-email", 25),  # 用户名太短
        ("alice", "<EMAIL>", -5),  # 年龄无效
        ("bob", "<EMAIL>", 30),  # 正常用户
        ("bob", "<EMAIL>", 25),  # 重复用户名
    ]
    
    for username, email, age in test_cases:
        try:
            user = user_manager.create_user(username, email, age)
            print(f"✓ 用户 {username} 创建成功")
        except (ValidationError, BusinessLogicError) as e:
            print(f"✗ 用户 {username} 创建失败: {e}")
    
    # 测试获取和更新用户
    try:
        user = user_manager.get_user("bob")
        print(f"✓ 获取用户: {user.username}, {user.age}岁")
        
        user_manager.update_user_age("bob", 31)
        print(f"✓ 用户年龄更新成功")
        
        user_manager.get_user("nonexistent")
    except (ResourceError, ValidationError) as e:
        print(f"✗ 操作失败: {e}")
    
    print()
    
    # 3. 文件操作异常处理
    print("=== 文件操作异常处理 ===")
    processor = FileProcessor()
    
    # 测试读取不存在的文件
    try:
        content = processor.safe_read_file("nonexistent.txt")
    except ResourceError as e:
        print(f"✗ 文件读取失败: {e}")
    
    # 测试写入文件
    try:
        processor.safe_write_file("test_output.txt", "测试内容")
        print("✓ 文件写入成功")
    except ResourceError as e:
        print(f"✗ 文件写入失败: {e}")
    
    print()
    
    # 4. 上下文管理器和异常链
    print("=== 上下文管理器和异常链 ===")
    
    # 正常连接
    try:
        with DatabaseConnection("valid_connection") as db:
            print("✓ 数据库操作成功")
    except ResourceError as e:
        print(f"✗ 数据库操作失败: {e}")
    
    # 无效连接
    try:
        with DatabaseConnection("invalid_connection") as db:
            print("这行不会执行")
    except ResourceError as e:
        print(f"✗ 数据库连接失败: {e}")
        print(f"原始异常: {e.__cause__}")
    
    print()
    
    # 5. 最佳实践
    demonstrate_best_practices()


if __name__ == "__main__":
    main()
