"""
面向对象编程练习题
完成这些练习来巩固OOP基础概念
"""

# 练习1: 创建一个学生类
class Student:
    """
    TODO: 完成学生类的实现
    要求:
    1. 包含姓名、学号、年级、成绩列表等属性
    2. 实现添加成绩、计算平均分、判断是否及格等方法
    3. 使用适当的封装（私有/受保护属性）
    4. 实现__str__和__repr__方法
    """
    
    # 类属性：总学生数
    total_students = 0
    
    def __init__(self, name, student_id, grade):
        # TODO: 初始化学生属性
        pass
    
    def add_score(self, subject, score):
        """添加成绩"""
        # TODO: 实现添加成绩功能，需要验证分数范围(0-100)
        pass
    
    def get_average_score(self):
        """计算平均分"""
        # TODO: 计算所有科目的平均分
        pass
    
    def is_passing(self, passing_score=60):
        """判断是否及格"""
        # TODO: 判断平均分是否达到及格线
        pass
    
    @classmethod
    def get_total_students(cls):
        """获取总学生数"""
        # TODO: 返回总学生数
        pass
    
    @staticmethod
    def validate_score(score):
        """验证分数是否有效"""
        # TODO: 验证分数是否在0-100范围内
        pass


# 练习2: 创建一个图书类和图书馆类
class Book:
    """
    TODO: 完成图书类
    要求:
    1. 包含书名、作者、ISBN、是否可借等属性
    2. 实现借书、还书方法
    3. 使用property装饰器管理可借状态
    """
    
    def __init__(self, title, author, isbn):
        # TODO: 初始化图书属性
        pass
    
    @property
    def is_available(self):
        """图书是否可借"""
        # TODO: 返回图书可借状态
        pass
    
    def borrow(self):
        """借书"""
        # TODO: 实现借书逻辑
        pass
    
    def return_book(self):
        """还书"""
        # TODO: 实现还书逻辑
        pass


class Library:
    """
    TODO: 完成图书馆类
    要求:
    1. 管理图书集合
    2. 实现添加图书、查找图书、借书、还书等功能
    3. 统计可借图书数量
    """
    
    def __init__(self, name):
        # TODO: 初始化图书馆
        pass
    
    def add_book(self, book):
        """添加图书"""
        # TODO: 添加图书到图书馆
        pass
    
    def find_book(self, title):
        """查找图书"""
        # TODO: 根据书名查找图书
        pass
    
    def borrow_book(self, title):
        """借书"""
        # TODO: 实现借书功能
        pass
    
    def return_book(self, title):
        """还书"""
        # TODO: 实现还书功能
        pass
    
    @property
    def available_books_count(self):
        """可借图书数量"""
        # TODO: 统计可借图书数量
        pass


# 练习3: 创建一个车辆类层次结构
class Vehicle:
    """
    TODO: 创建车辆基类
    要求:
    1. 包含品牌、型号、年份等基本属性
    2. 实现启动、停止等基本方法
    3. 为后续继承做准备
    """
    
    def __init__(self, brand, model, year):
        # TODO: 初始化车辆属性
        pass
    
    def start(self):
        """启动车辆"""
        # TODO: 实现启动逻辑
        pass
    
    def stop(self):
        """停止车辆"""
        # TODO: 实现停止逻辑
        pass
    
    def get_info(self):
        """获取车辆信息"""
        # TODO: 返回车辆基本信息
        pass


# 测试代码
def test_student():
    """测试学生类"""
    print("=== 测试学生类 ===")
    # TODO: 创建学生对象并测试各种方法
    pass


def test_library():
    """测试图书馆系统"""
    print("=== 测试图书馆系统 ===")
    # TODO: 创建图书和图书馆对象，测试借还书功能
    pass


def test_vehicle():
    """测试车辆类"""
    print("=== 测试车辆类 ===")
    # TODO: 创建车辆对象并测试方法
    pass


if __name__ == "__main__":
    print("请完成上述练习，然后运行测试函数验证你的实现")
    print("完成后可以取消注释下面的测试代码:")
    print()
    
    # 取消注释来测试你的实现
    # test_student()
    # test_library()
    # test_vehicle()
