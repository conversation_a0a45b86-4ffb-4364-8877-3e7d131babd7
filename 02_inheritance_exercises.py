"""
继承和多态练习题
完成这些练习来巩固继承和多态概念
"""

from abc import ABC, abstractmethod

# 练习1: 员工管理系统
class Employee:
    """
    TODO: 完成员工基类
    要求:
    1. 包含姓名、员工ID、基本工资等属性
    2. 实现计算工资的基本方法
    3. 实现员工信息显示方法
    """
    
    def __init__(self, name, employee_id, base_salary):
        # TODO: 初始化员工属性
        pass
    
    def calculate_salary(self):
        """计算工资 - 基类实现"""
        # TODO: 返回基本工资
        pass
    
    def get_info(self):
        """获取员工信息"""
        # TODO: 返回员工基本信息
        pass


class FullTimeEmployee(Employee):
    """
    TODO: 全职员工类
    要求:
    1. 继承Employee
    2. 添加年假天数、保险等属性
    3. 重写工资计算方法（基本工资 + 奖金）
    """
    
    def __init__(self, name, employee_id, base_salary, bonus):
        # TODO: 调用父类构造函数并初始化额外属性
        pass
    
    def calculate_salary(self):
        """重写工资计算"""
        # TODO: 基本工资 + 奖金
        pass


class PartTimeEmployee(Employee):
    """
    TODO: 兼职员工类
    要求:
    1. 继承Employee
    2. 添加工作小时数、时薪等属性
    3. 重写工资计算方法（时薪 * 工作小时）
    """
    
    def __init__(self, name, employee_id, hourly_rate, hours_worked):
        # TODO: 初始化兼职员工属性
        pass
    
    def calculate_salary(self):
        """重写工资计算"""
        # TODO: 时薪 * 工作小时
        pass


class Manager(FullTimeEmployee):
    """
    TODO: 经理类
    要求:
    1. 继承FullTimeEmployee
    2. 添加管理的员工列表
    3. 添加管理津贴
    4. 重写工资计算方法
    """
    
    def __init__(self, name, employee_id, base_salary, bonus, management_allowance):
        # TODO: 初始化经理属性
        pass
    
    def calculate_salary(self):
        """重写工资计算"""
        # TODO: 全职员工工资 + 管理津贴
        pass
    
    def add_subordinate(self, employee):
        """添加下属"""
        # TODO: 添加员工到管理列表
        pass


# 练习2: 交通工具系统
class Vehicle(ABC):
    """
    TODO: 抽象交通工具类
    要求:
    1. 使用ABC创建抽象基类
    2. 定义抽象方法：start_engine, stop_engine, get_max_speed
    3. 实现通用方法：get_info
    """
    
    def __init__(self, brand, model, year):
        # TODO: 初始化交通工具属性
        pass
    
    @abstractmethod
    def start_engine(self):
        """启动引擎 - 抽象方法"""
        pass
    
    @abstractmethod
    def stop_engine(self):
        """停止引擎 - 抽象方法"""
        pass
    
    @abstractmethod
    def get_max_speed(self):
        """获取最大速度 - 抽象方法"""
        pass
    
    def get_info(self):
        """获取车辆信息"""
        # TODO: 返回车辆基本信息
        pass


class Car(Vehicle):
    """
    TODO: 汽车类
    要求:
    1. 继承Vehicle
    2. 实现所有抽象方法
    3. 添加汽车特有的属性和方法
    """
    
    def __init__(self, brand, model, year, doors):
        # TODO: 初始化汽车属性
        pass
    
    def start_engine(self):
        # TODO: 实现汽车启动
        pass
    
    def stop_engine(self):
        # TODO: 实现汽车停止
        pass
    
    def get_max_speed(self):
        # TODO: 返回汽车最大速度
        pass


class Motorcycle(Vehicle):
    """
    TODO: 摩托车类
    要求:
    1. 继承Vehicle
    2. 实现所有抽象方法
    3. 添加摩托车特有的属性和方法
    """
    
    def __init__(self, brand, model, year, engine_size):
        # TODO: 初始化摩托车属性
        pass
    
    def start_engine(self):
        # TODO: 实现摩托车启动
        pass
    
    def stop_engine(self):
        # TODO: 实现摩托车停止
        pass
    
    def get_max_speed(self):
        # TODO: 返回摩托车最大速度
        pass


# 练习3: 多重继承 - 智能设备
class Connectable:
    """
    TODO: 可连接混入类
    要求:
    1. 提供网络连接功能
    2. 实现连接、断开连接方法
    """
    
    def __init__(self):
        # TODO: 初始化连接状态
        pass
    
    def connect(self):
        """连接网络"""
        # TODO: 实现连接逻辑
        pass
    
    def disconnect(self):
        """断开连接"""
        # TODO: 实现断开连接逻辑
        pass


class Controllable:
    """
    TODO: 可控制混入类
    要求:
    1. 提供远程控制功能
    2. 实现开启、关闭方法
    """
    
    def __init__(self):
        # TODO: 初始化控制状态
        pass
    
    def turn_on(self):
        """开启设备"""
        # TODO: 实现开启逻辑
        pass
    
    def turn_off(self):
        """关闭设备"""
        # TODO: 实现关闭逻辑
        pass


class SmartDevice:
    """
    TODO: 智能设备基类
    """
    
    def __init__(self, name, model):
        # TODO: 初始化设备属性
        pass


class SmartTV(SmartDevice, Connectable, Controllable):
    """
    TODO: 智能电视类
    要求:
    1. 多重继承SmartDevice, Connectable, Controllable
    2. 正确处理多重继承的初始化
    3. 添加电视特有功能
    """
    
    def __init__(self, name, model, screen_size):
        # TODO: 正确初始化所有父类
        pass
    
    def change_channel(self, channel):
        """换台"""
        # TODO: 实现换台功能
        pass


# 测试函数
def test_employee_system():
    """测试员工系统"""
    print("=== 测试员工系统 ===")
    # TODO: 创建不同类型的员工并测试多态
    pass


def test_vehicle_system():
    """测试交通工具系统"""
    print("=== 测试交通工具系统 ===")
    # TODO: 创建不同交通工具并测试抽象类和多态
    pass


def test_smart_device():
    """测试智能设备系统"""
    print("=== 测试智能设备系统 ===")
    # TODO: 测试多重继承
    pass


def demonstrate_polymorphism_exercise():
    """多态演示练习"""
    print("=== 多态演示练习 ===")
    # TODO: 创建不同对象的列表，演示多态调用
    pass


if __name__ == "__main__":
    print("请完成上述练习，然后运行测试函数验证你的实现")
    print("完成后可以取消注释下面的测试代码:")
    print()
    
    # 取消注释来测试你的实现
    # test_employee_system()
    # test_vehicle_system()
    # test_smart_device()
    # demonstrate_polymorphism_exercise()
