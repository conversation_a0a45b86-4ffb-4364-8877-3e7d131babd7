"""
为什么需要面向对象编程？
从最基础的问题开始理解
"""

print("=== 不用面向对象的麻烦方式 ===")

# 假设你要管理3个学生的信息
# 不用面向对象，你需要这样写：

# 学生1的信息
student1_name = "张三"
student1_age = 20
student1_grade = 85

# 学生2的信息  
student2_name = "李四"
student2_age = 21
student2_grade = 90

# 学生3的信息
student3_name = "王五"
student3_age = 19
student3_grade = 88

# 如果要让学生自我介绍，你需要写3个函数：
def student1_introduce():
    print(f"我是{student1_name}，{student1_age}岁，成绩{student1_grade}分")

def student2_introduce():
    print(f"我是{student2_name}，{student2_age}岁，成绩{student2_grade}分")

def student3_introduce():
    print(f"我是{student3_name}，{student3_age}岁，成绩{student3_grade}分")

# 调用函数
student1_introduce()
student2_introduce() 
student3_introduce()

print("\n问题：如果有100个学生，你要写300个变量和100个函数！太累了！\n")

print("=== 用面向对象的简单方式 ===")

# 用面向对象，你只需要写一个"模板"：
class Student:
    """学生类 - 这是一个模板"""
    
    def __init__(self, name, age, grade):
        """创建学生时需要填入的信息"""
        self.name = name
        self.age = age  
        self.grade = grade
    
    def introduce(self):
        """每个学生都会自我介绍"""
        print(f"我是{self.name}，{self.age}岁，成绩{self.grade}分")

# 现在创建学生变得超级简单：
student1 = Student("张三", 20, 85)
student2 = Student("李四", 21, 90)
student3 = Student("王五", 19, 88)

# 让他们自我介绍：
student1.introduce()
student2.introduce()
student3.introduce()

print("\n优势：即使有1000个学生，你也只需要写一个Student类！\n")

print("=== 为什么要用继承？===")

# 假设学校里不只有学生，还有老师
# 不用继承的话：
class Student_Old:
    def __init__(self, name, age, grade):
        self.name = name
        self.age = age
        self.grade = grade
    
    def introduce(self):
        print(f"我是学生{self.name}，{self.age}岁")
    
    def eat(self):
        print(f"{self.name}在吃饭")
    
    def sleep(self):
        print(f"{self.name}在睡觉")

class Teacher_Old:
    def __init__(self, name, age, subject):
        self.name = name
        self.age = age
        self.subject = subject
    
    def introduce(self):
        print(f"我是老师{self.name}，{self.age}岁")
    
    def eat(self):  # 重复代码！
        print(f"{self.name}在吃饭")
    
    def sleep(self):  # 重复代码！
        print(f"{self.name}在睡觉")

print("问题：学生和老师都会吃饭睡觉，代码重复了！")

# 用继承解决：
class Person:
    """人类 - 共同特征"""
    def __init__(self, name, age):
        self.name = name
        self.age = age
    
    def eat(self):
        print(f"{self.name}在吃饭")
    
    def sleep(self):
        print(f"{self.name}在睡觉")

class Student_New(Person):  # 学生继承人类的特征
    def __init__(self, name, age, grade):
        super().__init__(name, age)  # 使用父类的初始化
        self.grade = grade
    
    def study(self):
        print(f"学生{self.name}在学习")

class Teacher_New(Person):  # 老师也继承人类的特征
    def __init__(self, name, age, subject):
        super().__init__(name, age)
        self.subject = subject
    
    def teach(self):
        print(f"老师{self.name}在教{self.subject}")

# 测试继承
print("\n用继承后：")
student = Student_New("小明", 18, 95)
teacher = Teacher_New("王老师", 35, "数学")

# 他们都会吃饭睡觉（从Person继承）
student.eat()
teacher.eat()

# 他们还有自己的特殊能力
student.study()
teacher.teach()

print("\n=== 为什么要用装饰器？===")

# 假设你想记录每个函数的执行时间
import time

# 不用装饰器的麻烦方式：
def calculate_slow():
    start_time = time.time()  # 开始计时
    
    # 实际的计算
    total = 0
    for i in range(1000000):
        total += i
    
    end_time = time.time()    # 结束计时
    print(f"计算耗时：{end_time - start_time:.4f}秒")
    return total

def another_calculation():
    start_time = time.time()  # 又要写一遍计时代码！
    
    # 实际的计算
    result = 5 * 5 * 5
    
    end_time = time.time()
    print(f"计算耗时：{end_time - start_time:.4f}秒")
    return result

print("问题：每个函数都要写重复的计时代码！")

# 用装饰器的简单方式：
def timer(func):
    """计时装饰器"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__}耗时：{end_time - start_time:.4f}秒")
        return result
    return wrapper

@timer  # 只需要加这一行！
def fast_calculate():
    total = 0
    for i in range(1000000):
        total += i
    return total

@timer  # 只需要加这一行！
def another_fast_calculation():
    return 5 * 5 * 5

print("\n用装饰器后：")
fast_calculate()
another_fast_calculation()

print("\n=== 为什么要用异常处理？===")

# 不用异常处理的危险方式：
def dangerous_divide(a, b):
    result = a / b  # 如果b是0，程序会崩溃！
    return result

print("危险的除法：")
try:
    print(dangerous_divide(10, 2))  # 正常
    print(dangerous_divide(10, 0))  # 程序会崩溃！
except:
    print("程序崩溃了！")

# 用异常处理的安全方式：
def safe_divide(a, b):
    try:
        result = a / b
        return result
    except ZeroDivisionError:
        print("错误：不能除以0")
        return None

print("\n安全的除法：")
print(safe_divide(10, 2))  # 正常
print(safe_divide(10, 0))  # 不会崩溃，会给出友好提示

print("\n=== 总结：为什么要学这些？===")
print("1. 面向对象：避免重复代码，让代码更有组织")
print("2. 继承：共享相同功能，减少重复")  
print("3. 装饰器：给函数添加功能，不用修改原函数")
print("4. 异常处理：让程序更稳定，不会轻易崩溃")
print("5. 多线程：让程序能同时做多件事，提高效率")
print("\n这些都是为了让你写代码更轻松、更高效！")
