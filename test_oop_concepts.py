"""
测试面向对象编程概念
手动验证各种OOP特性
"""

# 简化的Person类用于测试
class Person:
    species = "Homo sapiens"
    population = 0
    
    def __init__(self, name, age):
        self.name = name
        self.age = age
        self._salary = 0
        self.__id = None
        Person.population += 1
    
    def introduce(self):
        return f"你好，我是{self.name}，今年{self.age}岁"
    
    def set_salary(self, salary):
        if salary < 0:
            raise ValueError("薪资不能为负数")
        self._salary = salary
    
    def get_salary(self):
        return self._salary
    
    @classmethod
    def get_population(cls):
        return cls.population
    
    @staticmethod
    def is_adult(age):
        return age >= 18

# 简化的BankAccount类
class BankAccount:
    def __init__(self, balance=0):
        self._balance = balance
    
    @property
    def balance(self):
        return self._balance
    
    @balance.setter
    def balance(self, amount):
        if amount < 0:
            raise ValueError("余额不能为负数")
        self._balance = amount
    
    def deposit(self, amount):
        if amount > 0:
            self._balance += amount
        return self._balance

def main():
    print("=== 面向对象编程概念测试 ===\n")
    
    # 1. 创建对象和基本操作
    print("1. 创建对象")
    person1 = Person("张三", 25)
    person2 = Person("李四", 30)
    
    print(f"person1: {person1.name}, {person1.age}岁")
    print(f"person2: {person2.name}, {person2.age}岁")
    print(f"总人口: {Person.get_population()}")
    print()
    
    # 2. 方法调用
    print("2. 方法调用")
    print(person1.introduce())
    print()
    
    # 3. 封装测试
    print("3. 封装测试")
    person1.set_salary(5000)
    print(f"{person1.name}的薪资: {person1.get_salary()}")
    
    try:
        person1.set_salary(-1000)
    except ValueError as e:
        print(f"错误捕获: {e}")
    print()
    
    # 4. 静态方法和类方法
    print("4. 静态方法和类方法")
    print(f"25岁是否成年: {Person.is_adult(25)}")
    print(f"17岁是否成年: {Person.is_adult(17)}")
    print(f"当前总人口: {Person.get_population()}")
    print()
    
    # 5. Property测试
    print("5. Property装饰器测试")
    account = BankAccount(1000)
    print(f"初始余额: {account.balance}")
    
    account.deposit(500)
    print(f"存款后余额: {account.balance}")
    
    # 直接设置余额（通过property）
    account.balance = 2000
    print(f"设置后余额: {account.balance}")
    
    try:
        account.balance = -100
    except ValueError as e:
        print(f"错误捕获: {e}")
    print()
    
    # 6. 类属性vs实例属性
    print("6. 类属性vs实例属性")
    print(f"person1的物种: {person1.species}")
    print(f"Person类的物种: {Person.species}")
    
    # 修改实例的species（创建实例属性）
    person1.species = "Homo superior"
    print(f"修改后person1的物种: {person1.species}")
    print(f"Person类的物种: {Person.species}")
    print(f"person2的物种: {person2.species}")

if __name__ == "__main__":
    main()
