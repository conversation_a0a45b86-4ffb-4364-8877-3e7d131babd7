"""
用最简单的例子理解面向对象编程
"""

# 1. 最简单的类 - 就像房子的设计图纸
class House:
    """房子类 - 这是设计图纸"""
    
    def __init__(self, address, color):
        """建造房子时需要的信息"""
        self.address = address  # 地址
        self.color = color      # 颜色
        self.lights_on = False  # 灯是否开着
    
    def turn_on_lights(self):
        """开灯"""
        self.lights_on = True
        print(f"{self.address}的房子开灯了")
    
    def turn_off_lights(self):
        """关灯"""
        self.lights_on = False
        print(f"{self.address}的房子关灯了")

# 现在我们用这个"设计图纸"建造真正的房子
print("=== 建造房子 ===")
# 建造第一栋房子
my_house = House("北京路123号", "红色")
print(f"我的房子在: {my_house.address}")
print(f"我的房子是: {my_house.color}")

# 建造第二栋房子
your_house = House("上海路456号", "蓝色")
print(f"你的房子在: {your_house.address}")
print(f"你的房子是: {your_house.color}")

# 操作房子
my_house.turn_on_lights()
your_house.turn_on_lights()
print()

# 2. 用人来理解类和对象
class Person:
    """人类 - 这是人的"模板""""
    
    def __init__(self, name, age):
        """创建一个人时需要的信息"""
        self.name = name    # 姓名
        self.age = age      # 年龄
        self.energy = 100   # 精力值
    
    def say_hello(self):
        """打招呼"""
        print(f"你好！我是{self.name}，今年{self.age}岁")
    
    def eat(self):
        """吃饭"""
        self.energy += 20
        print(f"{self.name}吃饭了，精力恢复到{self.energy}")
    
    def work(self):
        """工作"""
        self.energy -= 30
        print(f"{self.name}工作了，精力降到{self.energy}")

print("=== 创建人物 ===")
# 创建两个人
zhang_san = Person("张三", 25)
li_si = Person("李四", 30)

# 让他们打招呼
zhang_san.say_hello()
li_si.say_hello()

# 让张三工作和吃饭
zhang_san.work()
zhang_san.eat()
print()

# 3. 用动物来理解继承
class Animal:
    """动物基类 - 所有动物的共同特征"""
    
    def __init__(self, name):
        self.name = name
        self.is_alive = True
    
    def eat(self):
        print(f"{self.name}在吃东西")
    
    def sleep(self):
        print(f"{self.name}在睡觉")

class Dog(Animal):  # Dog继承了Animal
    """狗类 - 继承了动物的特征，还有自己的特征"""
    
    def bark(self):
        """狗会叫"""
        print(f"{self.name}汪汪叫")

class Cat(Animal):  # Cat也继承了Animal
    """猫类 - 继承了动物的特征，还有自己的特征"""
    
    def meow(self):
        """猫会叫"""
        print(f"{self.name}喵喵叫")

print("=== 动物世界 ===")
# 创建动物
dog = Dog("旺财")
cat = Cat("咪咪")

# 它们都会吃和睡（从Animal继承来的）
dog.eat()
dog.sleep()
cat.eat()
cat.sleep()

# 它们还有自己的特殊能力
dog.bark()
cat.meow()
print()

# 4. 用银行账户理解封装
class BankAccount:
    """银行账户 - 演示封装概念"""
    
    def __init__(self, owner_name, initial_balance=0):
        self.owner_name = owner_name
        self._balance = initial_balance  # _表示这是"私有"的，不应该直接修改
    
    def deposit(self, amount):
        """存钱 - 安全的方式"""
        if amount > 0:
            self._balance += amount
            print(f"{self.owner_name}存入{amount}元，余额：{self._balance}元")
        else:
            print("存款金额必须大于0")
    
    def withdraw(self, amount):
        """取钱 - 安全的方式"""
        if amount > 0 and amount <= self._balance:
            self._balance -= amount
            print(f"{self.owner_name}取出{amount}元，余额：{self._balance}元")
        else:
            print("取款失败：金额无效或余额不足")
    
    def check_balance(self):
        """查看余额"""
        print(f"{self.owner_name}的账户余额：{self._balance}元")
        return self._balance

print("=== 银行账户 ===")
# 创建账户
account = BankAccount("张三", 1000)

# 正常操作
account.check_balance()
account.deposit(500)
account.withdraw(200)
account.check_balance()

# 尝试非法操作
account.withdraw(2000)  # 余额不足
account.deposit(-100)   # 负数存款
print()

# 5. 简单的装饰器例子
def say_please(func):
    """礼貌装饰器 - 让函数变得更礼貌"""
    def wrapper(*args, **kwargs):
        print("请", end="")  # 在前面加"请"
        result = func(*args, **kwargs)
        print("，谢谢！")     # 在后面加"谢谢"
        return result
    return wrapper

@say_please
def ask_for_help():
    """请求帮助"""
    print("帮我一下", end="")

print("=== 装饰器让函数更礼貌 ===")
ask_for_help()
print()

# 6. 简单的异常处理
def safe_divide(a, b):
    """安全的除法"""
    try:
        result = a / b
        print(f"{a} ÷ {b} = {result}")
        return result
    except ZeroDivisionError:
        print("错误：不能除以0！")
        return None
    except Exception as e:
        print(f"发生了其他错误：{e}")
        return None

print("=== 安全的计算 ===")
safe_divide(10, 2)   # 正常计算
safe_divide(10, 0)   # 除以0的错误
safe_divide(10, "a") # 类型错误
print()

print("🎉 这些就是面向对象编程的基本概念！")
print("- 类：就像设计图纸或模板")
print("- 对象：根据类创建的具体实例")
print("- 继承：子类可以使用父类的功能")
print("- 封装：隐藏内部细节，提供安全接口")
print("- 装饰器：给函数添加额外功能")
print("- 异常处理：优雅地处理错误情况")
