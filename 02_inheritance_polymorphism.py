"""
继承和多态教程
包含：单继承、多继承、方法重写、多态、super()的使用
"""

# 1. 基础继承
class Animal:
    """动物基类"""
    
    def __init__(self, name, species):
        self.name = name
        self.species = species
        self.is_alive = True
    
    def eat(self):
        return f"{self.name}正在吃东西"
    
    def sleep(self):
        return f"{self.name}正在睡觉"
    
    def make_sound(self):
        return f"{self.name}发出了声音"
    
    def __str__(self):
        return f"{self.species}: {self.name}"


class Dog(Animal):
    """狗类 - 继承自Animal"""
    
    def __init__(self, name, breed):
        # 调用父类构造函数
        super().__init__(name, "犬类")
        self.breed = breed
        self.loyalty = 100
    
    # 方法重写
    def make_sound(self):
        return f"{self.name}汪汪叫"
    
    # 新增方法
    def fetch(self):
        return f"{self.name}去捡球了"
    
    def wag_tail(self):
        return f"{self.name}摇尾巴表示开心"


class Cat(Animal):
    """猫类 - 继承自Animal"""
    
    def __init__(self, name, color):
        super().__init__(name, "猫科")
        self.color = color
        self.independence = 90
    
    # 方法重写
    def make_sound(self):
        return f"{self.name}喵喵叫"
    
    # 新增方法
    def climb_tree(self):
        return f"{self.name}爬到了树上"
    
    def purr(self):
        return f"{self.name}发出呼噜声"


# 2. 多层继承
class WorkingDog(Dog):
    """工作犬 - 继承自Dog"""
    
    def __init__(self, name, breed, job):
        super().__init__(name, breed)
        self.job = job
        self.training_level = 0
    
    def work(self):
        return f"{self.name}正在执行{self.job}任务"
    
    def train(self):
        self.training_level += 10
        return f"{self.name}训练后等级提升到{self.training_level}"


# 3. 多重继承
class Flyable:
    """飞行能力混入类"""
    
    def __init__(self):
        self.can_fly = True
        self.altitude = 0
    
    def fly(self):
        self.altitude = 100
        return f"飞到了{self.altitude}米高度"
    
    def land(self):
        self.altitude = 0
        return "降落到地面"


class Swimmable:
    """游泳能力混入类"""
    
    def __init__(self):
        self.can_swim = True
        self.depth = 0
    
    def swim(self):
        self.depth = 10
        return f"游到了{self.depth}米深度"
    
    def surface(self):
        self.depth = 0
        return "浮出水面"


class Duck(Animal, Flyable, Swimmable):
    """鸭子 - 多重继承示例"""
    
    def __init__(self, name):
        # 注意：多重继承时需要小心初始化
        Animal.__init__(self, name, "鸭科")
        Flyable.__init__(self)
        Swimmable.__init__(self)
    
    def make_sound(self):
        return f"{self.name}嘎嘎叫"
    
    def dive(self):
        self.depth = 5
        return f"{self.name}潜水到{self.depth}米"


# 4. 抽象基类和多态
from abc import ABC, abstractmethod

class Shape(ABC):
    """抽象形状类"""
    
    def __init__(self, name):
        self.name = name
    
    @abstractmethod
    def area(self):
        """计算面积 - 抽象方法"""
        pass
    
    @abstractmethod
    def perimeter(self):
        """计算周长 - 抽象方法"""
        pass
    
    def describe(self):
        """通用描述方法"""
        return f"这是一个{self.name}，面积为{self.area():.2f}，周长为{self.perimeter():.2f}"


class Rectangle(Shape):
    """矩形类"""
    
    def __init__(self, width, height):
        super().__init__("矩形")
        self.width = width
        self.height = height
    
    def area(self):
        return self.width * self.height
    
    def perimeter(self):
        return 2 * (self.width + self.height)


class Circle(Shape):
    """圆形类"""
    
    def __init__(self, radius):
        super().__init__("圆形")
        self.radius = radius
    
    def area(self):
        return 3.14159 * self.radius ** 2
    
    def perimeter(self):
        return 2 * 3.14159 * self.radius


# 5. 方法解析顺序（MRO）演示
class A:
    def method(self):
        return "A的方法"

class B(A):
    def method(self):
        return "B的方法"

class C(A):
    def method(self):
        return "C的方法"

class D(B, C):
    pass  # 不重写method，看看会调用哪个


def demonstrate_polymorphism():
    """演示多态性"""
    print("=== 多态演示 ===")
    
    # 创建不同类型的动物
    animals = [
        Dog("旺财", "金毛"),
        Cat("咪咪", "橘色"),
        Duck("唐老鸭")
    ]
    
    # 多态：同样的方法调用，不同的行为
    for animal in animals:
        print(f"{animal}: {animal.make_sound()}")
    
    print()
    
    # 形状多态
    shapes = [
        Rectangle(5, 3),
        Circle(4)
    ]
    
    for shape in shapes:
        print(shape.describe())


def demonstrate_inheritance():
    """演示继承"""
    print("=== 继承演示 ===")
    
    # 基本继承
    dog = Dog("旺财", "拉布拉多")
    print(f"狗: {dog}")
    print(dog.eat())  # 继承的方法
    print(dog.make_sound())  # 重写的方法
    print(dog.fetch())  # 新增的方法
    print()
    
    # 多层继承
    police_dog = WorkingDog("警犬", "德牧", "缉毒")
    print(f"工作犬: {police_dog}")
    print(police_dog.work())
    print(police_dog.train())
    print(police_dog.make_sound())  # 继承自Dog的重写方法
    print()
    
    # 多重继承
    duck = Duck("小黄鸭")
    print(f"鸭子: {duck}")
    print(duck.make_sound())
    print(duck.fly())
    print(duck.swim())
    print(duck.dive())


def demonstrate_mro():
    """演示方法解析顺序"""
    print("=== 方法解析顺序(MRO)演示 ===")
    
    d = D()
    print(f"D类的方法调用结果: {d.method()}")
    print(f"D类的MRO: {D.__mro__}")
    print(f"Duck类的MRO: {Duck.__mro__}")


if __name__ == "__main__":
    print("=== 继承和多态教程 ===\n")
    
    demonstrate_inheritance()
    print()
    demonstrate_polymorphism()
    print()
    demonstrate_mro()
