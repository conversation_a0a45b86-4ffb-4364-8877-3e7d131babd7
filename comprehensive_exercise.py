"""
综合练习：Python高级概念整合
结合面向对象、多线程、异常处理、装饰器、上下文管理器等概念
创建一个完整的任务管理系统
"""

import threading
import time
import queue
import logging
from abc import ABC, abstractmethod
from contextlib import contextmanager
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Optional, Dict, Any
import json
from datetime import datetime
import functools

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 自定义异常
class TaskManagerError(Exception):
    """任务管理器基础异常"""
    pass

class TaskNotFoundError(TaskManagerError):
    """任务未找到异常"""
    pass

class TaskExecutionError(TaskManagerError):
    """任务执行异常"""
    pass

class InvalidTaskStateError(TaskManagerError):
    """无效任务状态异常"""
    pass

# 装饰器
def log_execution(func):
    """记录函数执行的装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        logger.info(f"开始执行 {func.__name__}")
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            logger.info(f"{func.__name__} 执行成功，耗时 {end_time - start_time:.2f} 秒")
            return result
        except Exception as e:
            logger.error(f"{func.__name__} 执行失败: {e}")
            raise
    return wrapper

def retry_on_failure(max_attempts=3, delay=1):
    """失败重试装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        raise
                    logger.warning(f"{func.__name__} 第 {attempt + 1} 次尝试失败: {e}，{delay}秒后重试")
                    time.sleep(delay)
        return wrapper
    return decorator

# 任务状态枚举
class TaskStatus:
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

# 抽象任务基类
class Task(ABC):
    """抽象任务基类"""
    
    def __init__(self, task_id: str, name: str, priority: int = 1):
        self.task_id = task_id
        self.name = name
        self.priority = priority
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        self.result: Any = None
        self.error: Optional[str] = None
        self._lock = threading.Lock()
    
    @abstractmethod
    def execute(self) -> Any:
        """执行任务的抽象方法"""
        pass
    
    def set_status(self, status: str):
        """设置任务状态"""
        with self._lock:
            valid_transitions = {
                TaskStatus.PENDING: [TaskStatus.RUNNING, TaskStatus.CANCELLED],
                TaskStatus.RUNNING: [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED],
                TaskStatus.COMPLETED: [],
                TaskStatus.FAILED: [TaskStatus.PENDING],  # 允许重试
                TaskStatus.CANCELLED: []
            }
            
            if status not in valid_transitions.get(self.status, []):
                raise InvalidTaskStateError(f"无法从 {self.status} 转换到 {status}")
            
            self.status = status
            if status == TaskStatus.RUNNING:
                self.started_at = datetime.now()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                self.completed_at = datetime.now()
    
    def run(self):
        """运行任务"""
        try:
            self.set_status(TaskStatus.RUNNING)
            logger.info(f"开始执行任务: {self.name}")
            
            self.result = self.execute()
            self.set_status(TaskStatus.COMPLETED)
            logger.info(f"任务完成: {self.name}")
            
        except Exception as e:
            self.error = str(e)
            self.set_status(TaskStatus.FAILED)
            logger.error(f"任务失败: {self.name}, 错误: {e}")
            raise TaskExecutionError(f"任务 {self.name} 执行失败: {e}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "name": self.name,
            "priority": self.priority,
            "status": self.status,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "result": self.result,
            "error": self.error
        }

# 具体任务实现
class ComputationTask(Task):
    """计算任务"""
    
    def __init__(self, task_id: str, name: str, numbers: List[int], operation: str = "sum"):
        super().__init__(task_id, name)
        self.numbers = numbers
        self.operation = operation
    
    @retry_on_failure(max_attempts=2)
    def execute(self) -> Any:
        """执行计算"""
        time.sleep(1)  # 模拟计算时间
        
        if self.operation == "sum":
            return sum(self.numbers)
        elif self.operation == "product":
            result = 1
            for num in self.numbers:
                result *= num
            return result
        elif self.operation == "average":
            return sum(self.numbers) / len(self.numbers)
        else:
            raise ValueError(f"不支持的操作: {self.operation}")

class FileProcessingTask(Task):
    """文件处理任务"""
    
    def __init__(self, task_id: str, name: str, file_path: str, operation: str = "count_lines"):
        super().__init__(task_id, name)
        self.file_path = file_path
        self.operation = operation
    
    def execute(self) -> Any:
        """执行文件处理"""
        try:
            if self.operation == "count_lines":
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    return len(f.readlines())
            elif self.operation == "word_count":
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    return len(content.split())
            else:
                raise ValueError(f"不支持的操作: {self.operation}")
        except FileNotFoundError:
            raise TaskExecutionError(f"文件不存在: {self.file_path}")

# 任务管理器
class TaskManager:
    """任务管理器"""
    
    def __init__(self, max_workers: int = 4):
        self.tasks: Dict[str, Task] = {}
        self.task_queue = queue.PriorityQueue()
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self._lock = threading.Lock()
        self._running = False
    
    def add_task(self, task: Task):
        """添加任务"""
        with self._lock:
            if task.task_id in self.tasks:
                raise TaskManagerError(f"任务ID {task.task_id} 已存在")
            
            self.tasks[task.task_id] = task
            # 优先级队列：负数表示高优先级
            self.task_queue.put((-task.priority, task.task_id))
            logger.info(f"添加任务: {task.name}")
    
    def get_task(self, task_id: str) -> Task:
        """获取任务"""
        if task_id not in self.tasks:
            raise TaskNotFoundError(f"任务 {task_id} 不存在")
        return self.tasks[task_id]
    
    def cancel_task(self, task_id: str):
        """取消任务"""
        task = self.get_task(task_id)
        if task.status == TaskStatus.PENDING:
            task.set_status(TaskStatus.CANCELLED)
            logger.info(f"任务已取消: {task.name}")
        else:
            raise InvalidTaskStateError(f"无法取消状态为 {task.status} 的任务")
    
    @contextmanager
    def task_execution_context(self, task: Task):
        """任务执行上下文管理器"""
        logger.info(f"进入任务执行上下文: {task.name}")
        try:
            yield task
        except Exception as e:
            logger.error(f"任务执行上下文异常: {e}")
            raise
        finally:
            logger.info(f"退出任务执行上下文: {task.name}")
    
    @log_execution
    def execute_task(self, task: Task):
        """执行单个任务"""
        with self.task_execution_context(task):
            task.run()
    
    def start_processing(self):
        """开始处理任务"""
        self._running = True
        logger.info("开始处理任务队列")
        
        futures = []
        while self._running and not self.task_queue.empty():
            try:
                # 获取任务（非阻塞）
                priority, task_id = self.task_queue.get_nowait()
                task = self.tasks[task_id]
                
                if task.status == TaskStatus.PENDING:
                    # 提交任务到线程池
                    future = self.executor.submit(self.execute_task, task)
                    futures.append((future, task))
                
            except queue.Empty:
                break
        
        # 等待所有任务完成
        for future, task in futures:
            try:
                future.result()  # 等待任务完成
            except TaskExecutionError as e:
                logger.error(f"任务执行失败: {e}")
    
    def stop_processing(self):
        """停止处理任务"""
        self._running = False
        self.executor.shutdown(wait=True)
        logger.info("任务处理已停止")
    
    def get_task_statistics(self) -> Dict[str, int]:
        """获取任务统计信息"""
        stats = {status: 0 for status in [
            TaskStatus.PENDING, TaskStatus.RUNNING, 
            TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED
        ]}
        
        for task in self.tasks.values():
            stats[task.status] += 1
        
        return stats
    
    def export_tasks_to_json(self, filename: str):
        """导出任务到JSON文件"""
        tasks_data = [task.to_dict() for task in self.tasks.values()]
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(tasks_data, f, ensure_ascii=False, indent=2)
        logger.info(f"任务数据已导出到: {filename}")

# 演示函数
def main():
    """主演示函数"""
    print("=== 综合练习：任务管理系统 ===\n")
    
    # 创建任务管理器
    manager = TaskManager(max_workers=3)
    
    # 创建各种任务
    tasks = [
        ComputationTask("calc_1", "计算1到100的和", list(range(1, 101)), "sum"),
        ComputationTask("calc_2", "计算1到10的乘积", list(range(1, 11)), "product"),
        ComputationTask("calc_3", "计算平均值", [10, 20, 30, 40, 50], "average"),
        FileProcessingTask("file_1", "统计当前文件行数", __file__, "count_lines"),
        ComputationTask("calc_4", "高优先级任务", [1, 2, 3], "sum"),
    ]
    
    # 设置优先级
    tasks[4].priority = 10  # 高优先级
    
    # 添加任务到管理器
    for task in tasks:
        try:
            manager.add_task(task)
        except TaskManagerError as e:
            print(f"添加任务失败: {e}")
    
    # 显示初始统计
    print("初始任务统计:")
    stats = manager.get_task_statistics()
    for status, count in stats.items():
        print(f"  {status}: {count}")
    print()
    
    # 开始处理任务
    try:
        manager.start_processing()
        
        # 等待一段时间让任务完成
        time.sleep(3)
        
        # 显示最终统计
        print("最终任务统计:")
        stats = manager.get_task_statistics()
        for status, count in stats.items():
            print(f"  {status}: {count}")
        print()
        
        # 显示任务结果
        print("任务结果:")
        for task_id, task in manager.tasks.items():
            if task.status == TaskStatus.COMPLETED:
                print(f"  ✓ {task.name}: {task.result}")
            elif task.status == TaskStatus.FAILED:
                print(f"  ✗ {task.name}: {task.error}")
        print()
        
        # 导出任务数据
        manager.export_tasks_to_json("task_results.json")
        
    except Exception as e:
        logger.error(f"任务处理过程中发生错误: {e}")
    
    finally:
        # 停止任务管理器
        manager.stop_processing()
    
    print("任务管理系统演示完成！")

if __name__ == "__main__":
    main()
